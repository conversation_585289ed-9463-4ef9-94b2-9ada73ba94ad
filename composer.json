{"name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.1", "bavix/laravel-wallet": "^10.0", "bezhansalleh/filament-shield": "^3.1", "filament/filament": "^3.2", "filament/forms": "^3.0", "filament/spatie-laravel-media-library-plugin": "3.0", "guzzlehttp/guzzle": "^7.2", "haruncpi/laravel-id-generator": "^1.1", "laravel/framework": "^10.10", "laravel/jetstream": "^4.1", "laravel/sanctum": "^3.3", "laravel/tinker": "^2.8", "livewire/livewire": "^3.0", "owenvoke/blade-fontawesome": "^2.4", "phpoffice/phpword": "^1.1", "psr/simple-cache": "^2.0", "pxlrbt/filament-excel": "^2.3", "realrashid/sweet-alert": "^7.1"}, "require-dev": {"fakerphp/faker": "^1.9.1", "laravel/pint": "^1.0", "laravel/sail": "^1.18", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^7.0", "phpunit/phpunit": "^10.1", "spatie/laravel-ignition": "^2.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["app/helpers.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi", "@php artisan filament:upgrade"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}