<?php

namespace App\Console\Commands;

use App\Models\Setting;
use App\Services\CurrencyService;
use Illuminate\Console\Command;

class TestCurrencyCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'currency:test {currency?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test currency formatting with different currencies';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $testAmounts = [1000, 15000.50, 250000, 1500000];

        if ($this->argument('currency')) {
            // Test specific currency
            $currency = strtoupper($this->argument('currency'));
            $this->info("Testing currency: {$currency}");

            try {
                CurrencyService::updateDefaultCurrency($currency);
                $this->info("✅ Currency updated to {$currency}");

                foreach ($testAmounts as $amount) {
                    $formatted = currency($amount);
                    $this->line("  {$amount} -> {$formatted}");
                }
            } catch (\Exception $e) {
                $this->error("❌ Error: " . $e->getMessage());
            }
        } else {
            // Test all available currencies
            $currencies = Setting::getAvailableCurrencies();

            $this->info('Testing all available currencies:');
            $this->newLine();

            foreach ($currencies as $code => $name) {
                $this->info("🔹 {$name} ({$code}):");

                try {
                    CurrencyService::updateDefaultCurrency($code);

                    foreach ($testAmounts as $amount) {
                        $formatted = currency($amount);
                        $this->line("  {$amount} -> {$formatted}");
                    }

                    $this->newLine();
                } catch (\Exception $e) {
                    $this->error("❌ Error with {$code}: " . $e->getMessage());
                }
            }

            // Reset to USD
            CurrencyService::updateDefaultCurrency('USD');
            $this->info('✅ Reset to USD');
        }

        $this->newLine();
        $this->info('Current configuration:');
        $config = CurrencyService::getConfiguration();
        foreach ($config as $key => $value) {
            $this->line("  {$key}: {$value}");
        }
    }
}
