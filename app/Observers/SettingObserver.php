<?php

namespace App\Observers;

use App\Models\Setting;
use App\Services\CurrencyService;

class SettingObserver
{
    /**
     * Handle the Setting "created" event.
     */
    public function created(Setting $setting): void
    {
        $this->clearCacheIfCurrencyRelated($setting);
    }

    /**
     * Handle the Setting "updated" event.
     */
    public function updated(Setting $setting): void
    {
        $this->clearCacheIfCurrencyRelated($setting);
    }

    /**
     * Handle the Setting "deleted" event.
     */
    public function deleted(Setting $setting): void
    {
        $this->clearCacheIfCurrencyRelated($setting);
    }

    /**
     * Handle the Setting "restored" event.
     */
    public function restored(Setting $setting): void
    {
        $this->clearCacheIfCurrencyRelated($setting);
    }

    /**
     * Handle the Setting "force deleted" event.
     */
    public function forceDeleted(Setting $setting): void
    {
        $this->clearCacheIfCurrencyRelated($setting);
    }

    /**
     * Clear currency caches if the setting is currency-related
     */
    private function clearCacheIfCurrencyRelated(Setting $setting): void
    {
        $currencyRelatedKeys = [
            'default_currency',
            'decimal_places',
            'currency_display_format'
        ];

        if (in_array($setting->key, $currencyRelatedKeys)) {
            CurrencyService::clearCurrencyCaches();
        }
    }
}
