<?php

use App\Helpers\CurrencyHelper;

if (!function_exists('currency')) {
    /**
     * Format amount with currency symbol
     */
    function currency(float|int $amount, ?string $currency = null): string
    {
        return CurrencyHelper::format($amount, $currency);
    }
}

if (!function_exists('currency_symbol')) {
    /**
     * Get currency symbol
     */
    function currency_symbol(?string $currency = null): string
    {
        return CurrencyHelper::getSymbol($currency);
    }
}

if (!function_exists('default_currency')) {
    /**
     * Get default currency
     */
    function default_currency(): string
    {
        return CurrencyHelper::getDefaultCurrency();
    }
}
