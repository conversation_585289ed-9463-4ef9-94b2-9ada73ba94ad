<?php

namespace App\Filament\Widgets;

use App\Services\LoanNotificationService;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class CompactCriticalAlerts extends BaseWidget
{
    protected static ?int $sort = 2;

    protected static ?string $pollingInterval = '30s';

    protected function getStats(): array
    {
        $notificationService = app(LoanNotificationService::class);
        $stats = $notificationService->getNotificationStats();

        return [
            Stat::make('Critical Alerts', $stats['critical_notifications'])
                ->description('Require immediate action')
                ->descriptionIcon('heroicon-m-exclamation-triangle')
                ->color($stats['critical_notifications'] > 0 ? 'danger' : 'success')
                ->chart($stats['critical_notifications'] > 0 ? [1, 2, 3, 5, 8, 13, 21] : [0, 0, 0, 0, 0, 0, 0])
                ->url(route('filament.admin.resources.loan-notifications.index', [
                    'tableFilters[priority][value]' => 'critical'
                ])),

            Stat::make('Overdue Loans', $stats['overdue_loans'])
                ->description('Past due payments')
                ->descriptionIcon('heroicon-m-clock')
                ->color($stats['overdue_loans'] > 0 ? 'warning' : 'success')
                ->chart($stats['overdue_loans'] > 0 ? [2, 4, 6, 8, 10, 12, 14] : [0, 0, 0, 0, 0, 0, 0])
                ->url(route('filament.admin.resources.loan-notifications.index', [
                    'tableFilters[type][value]' => 'overdue'
                ])),
        ];
    }
}
