<?php

namespace App\Filament\Widgets;

use App\Models\Loan;
use App\Models\Repayments;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Support\Number;

class MinimalPortfolioStats extends BaseWidget
{
    protected static ?int $sort = 1;

    protected static ?string $pollingInterval = '30s';

    protected function getStats(): array
    {
        // Get only essential metrics for minimal dashboard
        $totalPortfolioValue = Loan::whereIn('loan_status', ['approved', 'disbursed', 'defaulted'])->sum('principal_amount');
        $activeLoans = Loan::whereIn('loan_status', ['approved', 'disbursed'])->where('balance', '>', 0)->count();
        $totalRepayments = Repayments::sum('payments');
        
        // Calculate collection rate
        $collectionRate = $totalPortfolioValue > 0 ? ($totalRepayments / $totalPortfolioValue) * 100 : 0;

        return [
            Stat::make('Total Portfolio Value', currency($totalPortfolioValue))
                ->description('Total loans disbursed')
                ->descriptionIcon('heroicon-m-banknotes')
                ->color('success')
                ->chart([7, 12, 15, 18, 22, 25, 28])
                ->url(route('filament.admin.resources.loans.index')),

            Stat::make('Collection Rate', Number::format($collectionRate, 1) . '%')
                ->description('Repayments vs disbursements')
                ->descriptionIcon($collectionRate >= 80 ? 'heroicon-m-arrow-trending-up' : 'heroicon-m-arrow-trending-down')
                ->color($collectionRate >= 80 ? 'success' : ($collectionRate >= 60 ? 'warning' : 'danger'))
                ->chart([65, 70, 75, 78, 82, 85, $collectionRate])
                ->url(route('filament.admin.resources.repayments.index')),

            Stat::make('Active Loans', Number::format($activeLoans, 0))
                ->description('Currently outstanding')
                ->descriptionIcon('heroicon-m-document-text')
                ->color('info')
                ->chart([2, 4, 6, 8, 10, 12, $activeLoans])
                ->url(route('filament.admin.resources.loans.index', [
                    'tableFilters[loan_status][value]' => 'disbursed'
                ])),
        ];
    }
}
