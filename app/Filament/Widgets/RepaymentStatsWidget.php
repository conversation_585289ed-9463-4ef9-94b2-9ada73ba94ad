<?php

namespace App\Filament\Widgets;

use App\Models\Repayments;
use App\Models\Loan;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Support\Number;

class RepaymentStatsWidget extends BaseWidget
{
    protected static ?int $sort = 1;

    protected static ?string $pollingInterval = '30s';

    protected function getStats(): array
    {
        // Get repayment statistics
        $totalRepayments = Repayments::sum('payments');
        $totalRepaymentsCount = Repayments::count();
        $averageRepayment = $totalRepaymentsCount > 0 ? $totalRepayments / $totalRepaymentsCount : 0;
        
        $repaymentsThisMonth = Repayments::whereMonth('created_at', now()->month)
            ->whereYear('created_at', now()->year)
            ->sum('payments');
            
        $repaymentsCountThisMonth = Repayments::whereMonth('created_at', now()->month)
            ->whereYear('created_at', now()->year)
            ->count();

        return [
            Stat::make('Total Collected', currency($totalRepayments))
                ->description('All time repayments')
                ->descriptionIcon('heroicon-m-banknotes')
                ->color('success')
                ->chart([5, 10, 15, 20, 25, 30, 35]),

            Stat::make('Total Transactions', Number::format($totalRepaymentsCount, 0))
                ->description('Number of payments')
                ->descriptionIcon('heroicon-m-receipt-percent')
                ->color('info')
                ->chart([2, 4, 6, 8, 10, 12, 14]),

            Stat::make('Average Payment', currency($averageRepayment))
                ->description('Per transaction')
                ->descriptionIcon('heroicon-m-calculator')
                ->color('warning')
                ->chart([10, 12, 14, 16, 18, 20, 22]),

            Stat::make('This Month', '$' . Number::format($repaymentsThisMonth, 0))
                ->description("{$repaymentsCountThisMonth} payments received")
                ->descriptionIcon('heroicon-m-calendar-days')
                ->color('primary')
                ->chart([1, 3, 5, 7, 9, 11, 13]),
        ];
    }
}
