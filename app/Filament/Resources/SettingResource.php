<?php

namespace App\Filament\Resources;

use App\Filament\Resources\SettingResource\Pages;
use App\Filament\Resources\SettingResource\RelationManagers;
use App\Models\Setting;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class SettingResource extends Resource
{
    protected static ?string $model = Setting::class;

    protected static ?string $navigationIcon = 'heroicon-o-cog-6-tooth';

    protected static ?string $navigationGroup = 'Settings';

    protected static ?string $navigationLabel = 'Application Settings';

    protected static ?int $navigationSort = 2;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Basic Information')
                    ->schema([
                        Forms\Components\TextInput::make('key')
                            ->required()
                            ->maxLength(255)
                            ->unique(ignoreRecord: true)
                            ->label('Setting Key')
                            ->helperText('Unique identifier for this setting'),
                        Forms\Components\TextInput::make('name')
                            ->required()
                            ->maxLength(255)
                            ->label('Display Name'),
                        Forms\Components\Textarea::make('description')
                            ->columnSpanFull()
                            ->label('Description')
                            ->helperText('Explain what this setting controls'),
                        Forms\Components\Select::make('category')
                            ->required()
                            ->options([
                                'general' => 'General Settings',
                                'currency' => 'Currency Settings',
                                'display' => 'Display Settings',
                                'system' => 'System Settings',
                                'security' => 'Security Settings',
                            ])
                            ->label('Category'),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('Setting Configuration')
                    ->schema([
                        Forms\Components\Select::make('type')
                            ->required()
                            ->options([
                                'string' => 'Text',
                                'integer' => 'Number',
                                'decimal' => 'Decimal',
                                'boolean' => 'Yes/No',
                                'currency' => 'Currency',
                                'select' => 'Select Option',
                            ])
                            ->live()
                            ->label('Setting Type'),

                        Forms\Components\Select::make('value')
                            ->label('Currency')
                            ->options(Setting::getAvailableCurrencies())
                            ->searchable()
                            ->visible(fn (Forms\Get $get): bool => $get('type') === 'currency')
                            ->required(fn (Forms\Get $get): bool => $get('type') === 'currency'),

                        Forms\Components\TextInput::make('value')
                            ->label('Value')
                            ->visible(fn (Forms\Get $get): bool => in_array($get('type'), ['string', 'integer', 'decimal']))
                            ->required(fn (Forms\Get $get): bool => in_array($get('type'), ['string', 'integer', 'decimal'])),

                        Forms\Components\Toggle::make('value')
                            ->label('Enabled')
                            ->visible(fn (Forms\Get $get): bool => $get('type') === 'boolean')
                            ->required(fn (Forms\Get $get): bool => $get('type') === 'boolean'),

                        Forms\Components\Toggle::make('is_active')
                            ->required()
                            ->default(true)
                            ->label('Active')
                            ->helperText('Inactive settings will not be used'),
                    ])
                    ->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->searchable()
                    ->sortable()
                    ->label('Setting Name'),
                Tables\Columns\TextColumn::make('key')
                    ->searchable()
                    ->sortable()
                    ->label('Key')
                    ->fontFamily('mono')
                    ->copyable(),
                Tables\Columns\TextColumn::make('category')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'general' => 'gray',
                        'currency' => 'success',
                        'display' => 'info',
                        'system' => 'warning',
                        'security' => 'danger',
                        default => 'gray',
                    })
                    ->label('Category'),
                Tables\Columns\TextColumn::make('formatted_value')
                    ->label('Current Value')
                    ->badge()
                    ->color('primary'),
                Tables\Columns\TextColumn::make('type')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'currency' => 'success',
                        'boolean' => 'info',
                        'integer', 'decimal' => 'warning',
                        default => 'gray',
                    })
                    ->label('Type'),
                Tables\Columns\IconColumn::make('is_active')
                    ->boolean()
                    ->label('Active'),
                Tables\Columns\TextColumn::make('description')
                    ->limit(50)
                    ->tooltip(function (Tables\Columns\TextColumn $column): ?string {
                        $state = $column->getState();
                        if (strlen($state) <= 50) {
                            return null;
                        }
                        return $state;
                    })
                    ->label('Description')
                    ->toggleable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('category')
                    ->options([
                        'general' => 'General Settings',
                        'currency' => 'Currency Settings',
                        'display' => 'Display Settings',
                        'system' => 'System Settings',
                        'security' => 'Security Settings',
                    ])
                    ->label('Category'),
                Tables\Filters\SelectFilter::make('type')
                    ->options([
                        'string' => 'Text',
                        'integer' => 'Number',
                        'decimal' => 'Decimal',
                        'boolean' => 'Yes/No',
                        'currency' => 'Currency',
                        'select' => 'Select Option',
                    ])
                    ->label('Type'),
                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('Active Status'),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSettings::route('/'),
            'create' => Pages\CreateSetting::route('/create'),
            'edit' => Pages\EditSetting::route('/{record}/edit'),
        ];
    }
}
