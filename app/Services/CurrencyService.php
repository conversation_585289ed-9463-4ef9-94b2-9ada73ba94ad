<?php

namespace App\Services;

use App\Models\Setting;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Artisan;

class CurrencyService
{
    /**
     * Update the default currency and clear related caches
     */
    public static function updateDefaultCurrency(string $currency): void
    {
        // Validate currency
        $availableCurrencies = Setting::getAvailableCurrencies();
        if (!array_key_exists($currency, $availableCurrencies)) {
            throw new \InvalidArgumentException("Currency {$currency} is not supported");
        }

        // Update the setting
        Setting::set('default_currency', $currency);

        // Clear all currency-related caches
        self::clearCurrencyCaches();

        // Log the change
        Log::info("Default currency changed to {$currency}");
    }

    /**
     * Clear all currency-related caches
     */
    public static function clearCurrencyCaches(): void
    {
        // Clear setting caches
        Cache::forget('setting_default_currency');
        Cache::forget('setting_decimal_places');
        Cache::forget('setting_currency_display_format');

        // Clear view cache to refresh widgets
        try {
            Artisan::call('view:clear');
        } catch (\Exception) {
            // Ignore if view:clear fails
        }
    }

    /**
     * Get formatted currency for display throughout the app
     */
    public static function formatAmount(float|int $amount): string
    {
        return \App\Helpers\CurrencyHelper::format($amount);
    }

    /**
     * Get current currency symbol
     */
    public static function getCurrentSymbol(): string
    {
        $currency = Setting::get('default_currency', 'USD');
        return Setting::getCurrencySymbol($currency);
    }

    /**
     * Get current currency code
     */
    public static function getCurrentCurrency(): string
    {
        return Setting::get('default_currency', 'USD');
    }

    /**
     * Format amount for specific contexts
     */
    public static function formatForContext(float|int $amount, string $context = 'default'): string
    {
        return match($context) {
            'widget' => self::formatAmount($amount),
            'table' => self::formatAmount($amount),
            'form' => self::formatAmount($amount),
            'export' => self::formatAmount($amount),
            'api' => self::formatAmount($amount),
            default => self::formatAmount($amount),
        };
    }

    /**
     * Get currency configuration
     */
    public static function getConfiguration(): array
    {
        return [
            'currency' => self::getCurrentCurrency(),
            'symbol' => self::getCurrentSymbol(),
            'decimal_places' => (int) Setting::get('decimal_places', 2),
            'display_format' => Setting::get('currency_display_format', 'symbol_amount'),
        ];
    }

    /**
     * Apply currency formatting to an array of amounts
     */
    public static function formatAmounts(array $amounts): array
    {
        return array_map(fn($amount) => self::formatAmount($amount), $amounts);
    }
}
