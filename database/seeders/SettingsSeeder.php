<?php

namespace Database\Seeders;

use App\Models\Setting;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class SettingsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $settings = [
            // Currency Settings
            [
                'key' => 'default_currency',
                'name' => 'Default Currency',
                'description' => 'The default currency for the application',
                'value' => 'USD',
                'type' => 'currency',
                'validation_rules' => ['required', 'string'],
                'category' => 'currency',
                'is_active' => true,
            ],
            [
                'key' => 'currency_display_format',
                'name' => 'Currency Display Format',
                'description' => 'How to display currency amounts (symbol_amount or amount_symbol)',
                'value' => 'symbol_amount',
                'type' => 'select',
                'validation_rules' => ['required', 'string'],
                'options' => [
                    'symbol_amount' => 'Symbol then Amount ($100)',
                    'amount_symbol' => 'Amount then Symbol (100$)',
                ],
                'category' => 'currency',
                'is_active' => true,
            ],
            [
                'key' => 'decimal_places',
                'name' => 'Decimal Places',
                'description' => 'Number of decimal places to show for currency amounts',
                'value' => '2',
                'type' => 'integer',
                'validation_rules' => ['required', 'integer', 'min:0', 'max:4'],
                'category' => 'currency',
                'is_active' => true,
            ],

            // General Settings
            [
                'key' => 'app_name',
                'name' => 'Application Name',
                'description' => 'The name of the application',
                'value' => 'ClearLoan',
                'type' => 'string',
                'validation_rules' => ['required', 'string', 'max:255'],
                'category' => 'general',
                'is_active' => true,
            ],
            [
                'key' => 'company_name',
                'name' => 'Company Name',
                'description' => 'The name of your company or organization',
                'value' => 'Your Company Name',
                'type' => 'string',
                'validation_rules' => ['required', 'string', 'max:255'],
                'category' => 'general',
                'is_active' => true,
            ],
            [
                'key' => 'timezone',
                'name' => 'Default Timezone',
                'description' => 'The default timezone for the application',
                'value' => 'UTC',
                'type' => 'string',
                'validation_rules' => ['required', 'string'],
                'category' => 'general',
                'is_active' => true,
            ],

            // Display Settings
            [
                'key' => 'date_format',
                'name' => 'Date Format',
                'description' => 'The format to display dates',
                'value' => 'Y-m-d',
                'type' => 'select',
                'validation_rules' => ['required', 'string'],
                'options' => [
                    'Y-m-d' => '2024-12-31',
                    'd/m/Y' => '31/12/2024',
                    'm/d/Y' => '12/31/2024',
                    'd-m-Y' => '31-12-2024',
                ],
                'category' => 'display',
                'is_active' => true,
            ],
            [
                'key' => 'items_per_page',
                'name' => 'Items Per Page',
                'description' => 'Default number of items to show per page in tables',
                'value' => '25',
                'type' => 'integer',
                'validation_rules' => ['required', 'integer', 'min:10', 'max:100'],
                'category' => 'display',
                'is_active' => true,
            ],

            // System Settings
            [
                'key' => 'maintenance_mode',
                'name' => 'Maintenance Mode',
                'description' => 'Enable maintenance mode to restrict access',
                'value' => '0',
                'type' => 'boolean',
                'validation_rules' => ['required', 'boolean'],
                'category' => 'system',
                'is_active' => true,
            ],
            [
                'key' => 'backup_frequency',
                'name' => 'Backup Frequency',
                'description' => 'How often to create automatic backups (in hours)',
                'value' => '24',
                'type' => 'integer',
                'validation_rules' => ['required', 'integer', 'min:1', 'max:168'],
                'category' => 'system',
                'is_active' => true,
            ],

            // Loan Settings
            [
                'key' => 'default_interest_rate',
                'name' => 'Default Interest Rate',
                'description' => 'Default interest rate for new loans (percentage)',
                'value' => '15.0',
                'type' => 'decimal',
                'validation_rules' => ['required', 'numeric', 'min:0', 'max:100'],
                'category' => 'general',
                'is_active' => true,
            ],
            [
                'key' => 'max_loan_amount',
                'name' => 'Maximum Loan Amount',
                'description' => 'Maximum amount that can be loaned',
                'value' => '100000',
                'type' => 'decimal',
                'validation_rules' => ['required', 'numeric', 'min:1000'],
                'category' => 'general',
                'is_active' => true,
            ],
        ];

        foreach ($settings as $setting) {
            Setting::updateOrCreate(
                ['key' => $setting['key']],
                $setting
            );
        }

        $this->command->info('Application settings seeded successfully!');
    }
}
