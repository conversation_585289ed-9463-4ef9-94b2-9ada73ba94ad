<?php

namespace Database\Seeders;

use App\Models\Setting;
use App\Models\Loan;
use App\Models\Borrower;
use App\Models\LoanType;
use Illuminate\Database\Seeder;

class CurrencyTestSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Test different currency settings
        $currencies = ['USD', 'EUR', 'GBP', 'UGX', 'KES', 'NGN'];
        
        $this->command->info('Testing currency settings...');
        
        foreach ($currencies as $currency) {
            $this->command->info("Testing with {$currency}:");
            
            // Update currency setting
            Setting::set('default_currency', $currency);
            
            // Test currency formatting
            $testAmounts = [1000, 15000.50, 250000, 1500000];
            
            foreach ($testAmounts as $amount) {
                $formatted = currency($amount);
                $this->command->info("  {$amount} -> {$formatted}");
            }
            
            $this->command->info('');
        }
        
        // Reset to USD
        Setting::set('default_currency', 'USD');
        
        // Create some test loans with different amounts to see currency formatting
        $this->createTestLoans();
        
        $this->command->info('Currency test data created successfully!');
    }
    
    private function createTestLoans(): void
    {
        // Get or create a borrower
        $borrower = Borrower::first();
        if (!$borrower) {
            $borrower = Borrower::create([
                'first_name' => 'Test',
                'last_name' => 'Borrower',
                'gender' => 'male',
                'dob' => '1990-01-01',
                'occupation' => 'Software Developer',
                'identification' => 'TEST123456',
                'mobile' => '+1234567890',
                'email' => '<EMAIL>',
                'address' => '123 Test Street',
                'city' => 'Test City',
                'province' => 'Test Province',
                'zipcode' => '12345',
            ]);
        }
        
        // Get or create a loan type
        $loanType = LoanType::first();
        if (!$loanType) {
            $loanType = LoanType::create([
                'loan_name' => 'Personal Loan',
                'interest_rate' => 15.0,
                'interest_cycle' => 'monthly',
            ]);
        }
        
        // Create test loans with various amounts
        $testAmounts = [
            5000,    // Small loan
            25000,   // Medium loan
            75000,   // Large loan
            150000,  // Very large loan
        ];

        foreach ($testAmounts as $index => $amount) {
            Loan::create([
                'borrower_id' => $borrower->id,
                'loan_type_id' => $loanType->id,
                'principal_amount' => $amount,
                'loan_status' => 'approved',
                'loan_release_date' => now()->format('Y-m-d'),
                'loan_duration' => '12',
                'duration_period' => 'months',
                'balance' => $amount,
                'interest_amount' => $amount * 0.15,
                'repayment_amount' => ($amount + ($amount * 0.15)) / 12,
                'loan_due_date' => now()->addYear()->format('Y-m-d'),
                'loan_number' => 'TEST-' . str_pad($index + 1, 4, '0', STR_PAD_LEFT),
                'transaction_reference' => 'TXN-' . uniqid(),
            ]);
        }
    }
}
