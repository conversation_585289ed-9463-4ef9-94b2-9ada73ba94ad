<?php

use App\Http\Controllers\BorrowersController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

Route::get('/', function () {
    return view('welcome');
});

Route::middleware([
    'auth:sanctum',
    config('jetstream.auth_session'),
    'verified',
])->group(function () {
    Route::get('/dashboard', function () {
        return view('dashboard');
    })->name('dashboard');

    Route::resource('borrower',BorrowersController::class);
});

// Test route to verify currency formatting
Route::get('/test-currency', function () {
    $testAmounts = [1000, 15000.50, 250000, 1500000];
    $results = [];

    foreach ($testAmounts as $amount) {
        $results[] = [
            'amount' => $amount,
            'formatted' => currency($amount),
            'symbol' => currency_symbol(),
            'currency' => default_currency()
        ];
    }

    return response()->json([
        'message' => 'Currency formatting test',
        'current_currency' => default_currency(),
        'current_symbol' => currency_symbol(),
        'test_results' => $results
    ]);
})->name('test-currency');
