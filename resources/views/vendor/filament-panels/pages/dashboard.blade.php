<x-filament-panels::page class="fi-dashboard-page">
    @push('styles')
        <link rel="stylesheet" href="{{ asset('css/dashboard.css') }}">
        <style>
            .dashboard-filter-card {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            }
            .metric-card {
                background: rgba(255, 255, 255, 0.95);
                backdrop-filter: blur(10px);
                border: 1px solid rgba(255, 255, 255, 0.2);
            }
            .quick-actions-btn {
                background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
                box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            }
            .quick-actions-btn:hover {
                background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
                box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
                transform: translateY(-2px);
            }
            .dropdown-menu {
                background: rgba(255, 255, 255, 0.98);
                backdrop-filter: blur(20px);
                border: 1px solid rgba(255, 255, 255, 0.2);
                box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(255, 255, 255, 0.05);
            }
            .dropdown-item {
                transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
                position: relative;
                overflow: hidden;
            }
            .dropdown-item::before {
                content: '';
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
                transition: left 0.5s;
            }
            .dropdown-item:hover::before {
                left: 100%;
            }
            .dropdown-item:hover {
                transform: translateX(4px);
            }
            .action-icon {
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            }
            .dropdown-item:hover .action-icon {
                transform: scale(1.1) rotate(5deg);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            }
        </style>
    @endpush

    {{-- Clean Header with Filters --}}
    <div class="mb-8">
        <div class="dashboard-filter-card rounded-xl p-6 text-white shadow-lg">
            <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                <div>
                    <h1 class="text-3xl font-bold mb-2">
                        Dashboard Overview
                    </h1>
                    <p class="text-white/80 text-sm">
                        {{ now()->format('l, F j, Y') }} • {{ now()->format('g:i A') }}
                    </p>
                </div>
                
                {{-- Quick Filters --}}
                <div class="flex flex-wrap gap-3">
                    <a href="{{ route('filament.admin.resources.loans.index', ['tableFilters[loan_status][value]' => 'approved']) }}" 
                       class="px-4 py-2 bg-white/20 hover:bg-white/30 rounded-lg text-sm font-medium transition-colors">
                        Active Loans
                    </a>
                    <a href="{{ route('filament.admin.resources.loan-notifications.index', ['tableFilters[priority][value]' => 'critical']) }}" 
                       class="px-4 py-2 bg-white/20 hover:bg-white/30 rounded-lg text-sm font-medium transition-colors">
                        Critical Alerts
                    </a>
                    <a href="{{ route('filament.admin.resources.loans.index', ['tableFilters[loan_status][value]' => 'defaulted']) }}" 
                       class="px-4 py-2 bg-white/20 hover:bg-white/30 rounded-lg text-sm font-medium transition-colors">
                        Defaulted Loans
                    </a>
                    <a href="{{ route('filament.admin.resources.repayments.index') }}" 
                       class="px-4 py-2 bg-white/20 hover:bg-white/30 rounded-lg text-sm font-medium transition-colors">
                        Payments
                    </a>
                </div>
            </div>
        </div>
    </div>

    {{-- Quick Actions Dropdown --}}
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h2 class="text-xl font-bold text-gray-900 mb-1">Quick Actions</h2>
                <p class="text-sm text-gray-500">Perform common tasks quickly</p>
            </div>
            <div class="relative" x-data="{ open: false }">
                <button @click="open = !open"
                        class="quick-actions-btn inline-flex items-center px-6 py-3 text-white text-sm font-semibold rounded-xl focus:outline-none focus:ring-4 focus:ring-blue-500/30 group">
                    <div class="flex items-center">
                        <svg class="w-5 h-5 mr-3 transition-transform duration-300 group-hover:scale-110" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                        <span class="mr-3">Quick Actions</span>
                        <svg class="w-4 h-4 transition-all duration-300" :class="{ 'rotate-180 scale-110': open }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </div>
                </button>

                <div x-show="open"
                     @click.away="open = false"
                     x-transition:enter="transition ease-out duration-300"
                     x-transition:enter-start="opacity-0 scale-90 -translate-y-2"
                     x-transition:enter-end="opacity-100 scale-100 translate-y-0"
                     x-transition:leave="transition ease-in duration-200"
                     x-transition:leave-start="opacity-100 scale-100 translate-y-0"
                     x-transition:leave-end="opacity-0 scale-90 -translate-y-2"
                     class="dropdown-menu absolute right-0 mt-4 w-80 rounded-2xl z-50 overflow-hidden">

                    <div class="p-2">
                        <div class="text-xs font-semibold text-gray-400 uppercase tracking-wider px-4 py-2 mb-1">
                            Available Actions
                        </div>
                        <a href="{{ route('filament.admin.resources.loans.create') }}"
                           class="dropdown-item flex items-center px-4 py-4 text-gray-700 hover:bg-gradient-to-r hover:from-blue-50 hover:to-blue-100 hover:text-blue-800 rounded-xl mx-2 mb-1 group">
                            <div class="action-icon bg-gradient-to-br from-blue-500 to-blue-600 p-3 rounded-xl mr-4 shadow-lg">
                                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                </svg>
                            </div>
                            <div class="flex-1">
                                <div class="font-semibold text-base group-hover:text-blue-800">New Loan</div>
                                <div class="text-sm text-gray-500 group-hover:text-blue-600">Create loan application</div>
                            </div>
                            <svg class="w-4 h-4 text-gray-400 group-hover:text-blue-600 transition-all duration-200 group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                        </a>

                        <a href="{{ route('filament.admin.resources.repayments.create') }}"
                           class="dropdown-item flex items-center px-4 py-4 text-gray-700 hover:bg-gradient-to-r hover:from-green-50 hover:to-green-100 hover:text-green-800 rounded-xl mx-2 mb-1 group">
                            <div class="action-icon bg-gradient-to-br from-green-500 to-green-600 p-3 rounded-xl mr-4 shadow-lg">
                                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                </svg>
                            </div>
                            <div class="flex-1">
                                <div class="font-semibold text-base group-hover:text-green-800">Record Payment</div>
                                <div class="text-sm text-gray-500 group-hover:text-green-600">Process repayment</div>
                            </div>
                            <svg class="w-4 h-4 text-gray-400 group-hover:text-green-600 transition-all duration-200 group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                        </a>

                        <a href="{{ route('filament.admin.resources.borrowers.create') }}"
                           class="dropdown-item flex items-center px-4 py-4 text-gray-700 hover:bg-gradient-to-r hover:from-purple-50 hover:to-purple-100 hover:text-purple-800 rounded-xl mx-2 mb-1 group">
                            <div class="action-icon bg-gradient-to-br from-purple-500 to-purple-600 p-3 rounded-xl mr-4 shadow-lg">
                                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                </svg>
                            </div>
                            <div class="flex-1">
                                <div class="font-semibold text-base group-hover:text-purple-800">Add Borrower</div>
                                <div class="text-sm text-gray-500 group-hover:text-purple-600">Register customer</div>
                            </div>
                            <svg class="w-4 h-4 text-gray-400 group-hover:text-purple-600 transition-all duration-200 group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                        </a>

                        <a href="{{ route('filament.admin.resources.loan-notifications.index') }}"
                           class="dropdown-item flex items-center px-4 py-4 text-gray-700 hover:bg-gradient-to-r hover:from-red-50 hover:to-red-100 hover:text-red-800 rounded-xl mx-2 mb-1 group">
                            <div class="action-icon bg-gradient-to-br from-red-500 to-red-600 p-3 rounded-xl mr-4 shadow-lg">
                                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM4.828 4.828A4 4 0 715.5 4H9v1H5.5a3 3 0 00-2.121.879l-.707.707A1 1 0 002 7.414V11H1V7.414a2 2 0 01.586-1.414l.707-.707A5 5 0 715.5 3H9a1 1 0 011 1v5.586l4.707-4.707A1 1 0 0116 4h3a1 1 0 011 1v3a1 1 0 01-.293.707L15 13.414V9a1 1 0 00-1-1H9.414l-4.586 4.586z"></path>
                                </svg>
                            </div>
                            <div class="flex-1">
                                <div class="font-semibold text-base group-hover:text-red-800">Notifications</div>
                                <div class="text-sm text-gray-500 group-hover:text-red-600">View alerts</div>
                            </div>
                            <svg class="w-4 h-4 text-gray-400 group-hover:text-red-600 transition-all duration-200 group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                        </a>

                        {{-- Dropdown Footer --}}
                        <div class="border-t border-gray-100 mt-2 pt-3 px-4 pb-3">
                            <div class="flex items-center justify-between">
                                <div class="text-xs text-gray-400">
                                    <span class="font-medium">4 actions available</span>
                                </div>
                                <div class="flex items-center text-xs text-gray-400">
                                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                    </svg>
                                    Quick access
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {{-- Essential Metrics Only --}}
    <div class="space-y-8">
        {{-- Main Portfolio Metrics --}}
        <div class="metric-card rounded-xl p-6 mb-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Portfolio Overview</h3>
            @livewire(\App\Filament\Widgets\MinimalPortfolioStats::class)
        </div>

        {{-- Critical Alerts - Compact View --}}
        <div class="metric-card rounded-xl p-6 mb-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900">Critical Alerts</h3>
                <a href="{{ route('filament.admin.resources.loan-notifications.index') }}" 
                   class="text-sm text-blue-600 hover:text-blue-800 font-medium">
                    View All →
                </a>
            </div>
            @livewire(\App\Filament\Widgets\CompactCriticalAlerts::class)
        </div>

        {{-- Recent Activity Summary --}}
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div class="metric-card rounded-xl p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">Recent Activity</h3>
                    <a href="{{ route('filament.admin.resources.loans.index') }}" 
                       class="text-sm text-blue-600 hover:text-blue-800 font-medium">
                        View All →
                    </a>
                </div>
                @livewire(\App\Filament\Widgets\RecentActivityWidget::class)
            </div>
            
            <div class="metric-card rounded-xl p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">Loan Distribution</h3>
                    <a href="{{ route('filament.admin.resources.loans.index') }}" 
                       class="text-sm text-blue-600 hover:text-blue-800 font-medium">
                        View Details →
                    </a>
                </div>
                @livewire(\App\Filament\Widgets\LoanStatusChart::class)
            </div>
        </div>
    </div>

    {{-- Minimal Footer --}}
    <div class="mt-12 pt-6 border-t border-gray-100">
        <div class="text-center text-xs text-gray-400">
            <p>{{ config('app.name') }} • Last updated: {{ now()->format('M j, g:i A') }}</p>
        </div>
    </div>
</x-filament-panels::page>
