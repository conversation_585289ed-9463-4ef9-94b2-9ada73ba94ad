<x-filament-panels::page>
    @push('styles')
        <link rel="stylesheet" href="{{ asset('css/dashboard.css') }}">
        <style>
            .dashboard-filter-card {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            }
            .quick-action-card {
                transition: all 0.3s ease;
                backdrop-filter: blur(10px);
            }
            .quick-action-card:hover {
                transform: translateY(-2px);
                box-shadow: 0 10px 25px rgba(0,0,0,0.1);
            }
            .metric-card {
                background: rgba(255, 255, 255, 0.95);
                backdrop-filter: blur(10px);
                border: 1px solid rgba(255, 255, 255, 0.2);
            }
        </style>
    @endpush

    {{-- Clean Header with Filters --}}
    <div class="mb-8">
        <div class="dashboard-filter-card rounded-xl p-6 text-white shadow-lg">
            <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                <div>
                    <h1 class="text-3xl font-bold mb-2">
                        Dashboard Overview
                    </h1>
                    <p class="text-white/80 text-sm">
                        {{ now()->format('l, F j, Y') }} • {{ now()->format('g:i A') }}
                    </p>
                </div>

                {{-- Quick Filters --}}
                <div class="flex flex-wrap gap-3">
                    <a href="{{ route('filament.admin.resources.loans.index', ['tableFilters[loan_status][value]' => 'approved']) }}"
                       class="px-4 py-2 bg-white/20 hover:bg-white/30 rounded-lg text-sm font-medium transition-colors">
                        Active Loans
                    </a>
                    <a href="{{ route('filament.admin.resources.loan-notifications.index', ['tableFilters[priority][value]' => 'critical']) }}"
                       class="px-4 py-2 bg-white/20 hover:bg-white/30 rounded-lg text-sm font-medium transition-colors">
                        Critical Alerts
                    </a>
                    <a href="{{ route('filament.admin.resources.loans.index', ['tableFilters[loan_status][value]' => 'defaulted']) }}"
                       class="px-4 py-2 bg-white/20 hover:bg-white/30 rounded-lg text-sm font-medium transition-colors">
                        Defaulted Loans
                    </a>
                    <a href="{{ route('filament.admin.resources.repayments.index') }}"
                       class="px-4 py-2 bg-white/20 hover:bg-white/30 rounded-lg text-sm font-medium transition-colors">
                        Payments
                    </a>
                </div>
            </div>
        </div>
    </div>

    {{-- Essential Quick Actions --}}
    <div class="mb-8">
        <h2 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h2>
        <div class="grid grid-cols-4 gap-2 sm:gap-4">
            <a href="{{ route('filament.admin.resources.loans.create') }}"
               class="quick-action-card metric-card rounded-xl p-6 group">
                <div class="flex items-center">
                    <div class="bg-gradient-to-br from-blue-500 to-blue-600 p-3 rounded-lg mr-4 group-hover:scale-110 transition-transform">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                    </div>
                    <div>
                        <div class="font-semibold text-gray-900 text-lg">New Loan</div>
                        <div class="text-sm text-gray-500">Create application</div>
                    </div>
                </div>
            </a>

            <a href="{{ route('filament.admin.resources.repayments.create') }}"
               class="quick-action-card metric-card rounded-xl p-6 group">
                <div class="flex items-center">
                    <div class="bg-gradient-to-br from-green-500 to-green-600 p-3 rounded-lg mr-4 group-hover:scale-110 transition-transform">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                        </svg>
                    </div>
                    <div>
                        <div class="font-semibold text-gray-900 text-lg">Record Payment</div>
                        <div class="text-sm text-gray-500">Process repayment</div>
                    </div>
                </div>
            </a>

            <a href="{{ route('filament.admin.resources.borrowers.create') }}"
               class="quick-action-card metric-card rounded-xl p-6 group">
                <div class="flex items-center">
                    <div class="bg-gradient-to-br from-purple-500 to-purple-600 p-3 rounded-lg mr-4 group-hover:scale-110 transition-transform">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                        </svg>
                    </div>
                    <div>
                        <div class="font-semibold text-gray-900 text-lg">Add Borrower</div>
                        <div class="text-sm text-gray-500">Register customer</div>
                    </div>
                </div>
            </a>

            <a href="{{ route('filament.admin.resources.loan-notifications.index') }}"
               class="quick-action-card metric-card rounded-xl p-6 group">
                <div class="flex items-center">
                    <div class="bg-gradient-to-br from-red-500 to-red-600 p-3 rounded-lg mr-4 group-hover:scale-110 transition-transform">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM4.828 4.828A4 4 0 015.5 4H9v1H5.5a3 3 0 00-2.121.879l-.707.707A1 1 0 002 7.414V11H1V7.414a2 2 0 01.586-1.414l.707-.707A5 5 0 015.5 3H9a1 1 0 011 1v5.586l4.707-4.707A1 1 0 0116 4h3a1 1 0 011 1v3a1 1 0 01-.293.707L15 13.414V9a1 1 0 00-1-1H9.414l-4.586 4.586z"></path>
                        </svg>
                    </div>
                    <div>
                        <div class="font-semibold text-gray-900 text-lg">Notifications</div>
                        <div class="text-sm text-gray-500">View alerts</div>
                    </div>
                </div>
            </a>
        </div>
    </div>

    {{-- Essential Metrics Only --}}
    <div class="space-y-8">
        {{-- Main Portfolio Metrics --}}
        <div class="metric-card rounded-xl p-6 mb-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Portfolio Overview</h3>
            @livewire(\App\Filament\Widgets\MinimalPortfolioStats::class)
        </div>

        {{-- Critical Alerts - Compact View --}}
        <div class="metric-card rounded-xl p-6 mb-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900">Critical Alerts</h3>
                <a href="{{ route('filament.admin.resources.loan-notifications.index') }}"
                   class="text-sm text-blue-600 hover:text-blue-800 font-medium">
                    View All →
                </a>
            </div>
            @livewire(\App\Filament\Widgets\CompactCriticalAlerts::class)
        </div>

        {{-- Recent Activity Summary --}}
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div class="metric-card rounded-xl p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">Recent Activity</h3>
                    <a href="{{ route('filament.admin.resources.loans.index') }}"
                       class="text-sm text-blue-600 hover:text-blue-800 font-medium">
                        View All →
                    </a>
                </div>
                @livewire(\App\Filament\Widgets\RecentActivityWidget::class)
            </div>

            <div class="metric-card rounded-xl p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">Loan Distribution</h3>
                    <a href="{{ route('filament.admin.resources.loans.index') }}"
                       class="text-sm text-blue-600 hover:text-blue-800 font-medium">
                        View Details →
                    </a>
                </div>
                @livewire(\App\Filament\Widgets\LoanStatusChart::class)
            </div>
        </div>
    </div>

    {{-- Minimal Footer --}}
    <div class="mt-12 pt-6 border-t border-gray-100">
        <div class="text-center text-xs text-gray-400">
            <p>{{ config('app.name') }} • Last updated: {{ now()->format('M j, g:i A') }}</p>
        </div>
    </div>
</x-filament-panels::page>
